/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/check-pending-payment/route";
exports.ids = ["app/api/auth/check-pending-payment/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_check_pending_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/check-pending-payment/route.ts */ \"(rsc)/./src/app/api/auth/check-pending-payment/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/check-pending-payment/route\",\n        pathname: \"/api/auth/check-pending-payment\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/check-pending-payment/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\check-pending-payment\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_check_pending_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/check-pending-payment/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/auth/check-pending-payment/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/service-role */ \"(rsc)/./src/lib/supabase/service-role.ts\");\n\n\nasync function POST(req) {\n    console.log('🔥 CHECK-PENDING-PAYMENT: API called');\n    try {\n        const { email } = await req.json();\n        console.log('🔥 CHECK-PENDING-PAYMENT: Email received:', email);\n        if (!email) {\n            console.log('🔥 CHECK-PENDING-PAYMENT: No email provided');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_service_role__WEBPACK_IMPORTED_MODULE_1__.createServiceRoleClient)();\n        console.log('🔥 CHECK-PENDING-PAYMENT: Supabase client created');\n        // Check if user exists by email using getUserByEmail\n        console.log('🔥 CHECK-PENDING-PAYMENT: Looking up user by email...');\n        const { data: userData, error: userError } = await supabase.auth.admin.getUserByEmail(email);\n        if (userError) {\n            if (userError.message === 'User not found') {\n                console.log('🔥 CHECK-PENDING-PAYMENT: No user found with email:', email);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    exists: false,\n                    message: 'No account found with this email address.'\n                });\n            }\n            console.error('🔥 CHECK-PENDING-PAYMENT: Error with getUserByEmail:', userError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to check user status'\n            }, {\n                status: 500\n            });\n        }\n        if (!userData.user) {\n            console.log('🔥 CHECK-PENDING-PAYMENT: No user found with email:', email);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                exists: false,\n                message: 'No account found with this email address.'\n            });\n        }\n        const user = userData.user;\n        console.log('🔥 CHECK-PENDING-PAYMENT: Found user:', user.id);\n        // Check user profile for status\n        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status, user_status').eq('id', user.id).single();\n        console.log('🔥 CHECK-PENDING-PAYMENT: User profile:', profile);\n        if (profile) {\n            // Check if user has pending status\n            if (profile.user_status === 'pending') {\n                console.log('🔥 CHECK-PENDING-PAYMENT: User has pending status');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    exists: true,\n                    hasPendingPayment: true,\n                    plan: profile.subscription_tier,\n                    userId: user.id,\n                    message: `You have a pending payment for your ${profile.subscription_tier} plan. Please complete your checkout.`,\n                    signInUrl: `/auth/signin?message=complete_payment&plan=${profile.subscription_tier}&email=${encodeURIComponent(email)}`\n                });\n            }\n            // Check if user has active subscription\n            if (profile.user_status === 'active' && profile.subscription_status === 'active') {\n                console.log('🔥 CHECK-PENDING-PAYMENT: User has active subscription');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    exists: true,\n                    hasPendingPayment: false,\n                    hasActiveSubscription: true,\n                    tier: profile.subscription_tier,\n                    message: `You already have an active ${profile.subscription_tier} account. Please sign in to access your dashboard.`,\n                    signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`\n                });\n            }\n        }\n        // Fallback: Check legacy user metadata for pending payment status\n        const paymentStatus = user.user_metadata?.payment_status;\n        const userPlan = user.user_metadata?.plan;\n        console.log('🔥 CHECK-PENDING-PAYMENT: Checking legacy metadata - payment:', paymentStatus, 'plan:', userPlan);\n        if (paymentStatus === 'pending' && userPlan && [\n            'starter',\n            'professional',\n            'enterprise'\n        ].includes(userPlan)) {\n            console.log('🔥 CHECK-PENDING-PAYMENT: User has pending payment in metadata');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                exists: true,\n                hasPendingPayment: true,\n                plan: userPlan,\n                userId: user.id,\n                message: `You have a pending payment for your ${userPlan} plan. Please complete your checkout.`,\n                signInUrl: `/auth/signin?message=complete_payment&plan=${userPlan}&email=${encodeURIComponent(email)}`\n            });\n        }\n        // User exists but no clear status\n        console.log('🔥 CHECK-PENDING-PAYMENT: User exists but no clear status');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            exists: true,\n            hasPendingPayment: false,\n            hasActiveSubscription: false,\n            message: 'Account found. Please sign in to continue.',\n            signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`\n        });\n    } catch (error) {\n        console.error('🔥 CHECK-PENDING-PAYMENT: Exception:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint for testing\nasync function GET() {\n    console.log('🔥 CHECK-PENDING-PAYMENT: GET endpoint called');\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Pending payment check endpoint is working',\n        usage: 'POST /api/auth/check-pending-payment with { email: \"<EMAIL>\" }',\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/check-pending-payment/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/service-role.ts":
/*!******************************************!*\
  !*** ./src/lib/supabase/service-role.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Create service role client for server-side operations that need to bypass RLS\n * This client has full database access and should only be used for:\n * - Internal API operations\n * - Admin operations\n * - System-level operations\n * - External API key authentication\n */ function createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZpY2Utcm9sZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUVyRDs7Ozs7OztDQU9DLEdBQ00sU0FBU0M7SUFDZCxPQUFPRCxzR0FBWUEsQ0FDakJFLDBDQUFvQyxFQUNwQ0EsUUFBUUMsR0FBRyxDQUFDRSx5QkFBeUIsRUFDckM7UUFDRUMsTUFBTTtZQUNKQyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxsaWJcXHN1cGFiYXNlXFxzZXJ2aWNlLXJvbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLyoqXG4gKiBDcmVhdGUgc2VydmljZSByb2xlIGNsaWVudCBmb3Igc2VydmVyLXNpZGUgb3BlcmF0aW9ucyB0aGF0IG5lZWQgdG8gYnlwYXNzIFJMU1xuICogVGhpcyBjbGllbnQgaGFzIGZ1bGwgZGF0YWJhc2UgYWNjZXNzIGFuZCBzaG91bGQgb25seSBiZSB1c2VkIGZvcjpcbiAqIC0gSW50ZXJuYWwgQVBJIG9wZXJhdGlvbnNcbiAqIC0gQWRtaW4gb3BlcmF0aW9uc1xuICogLSBTeXN0ZW0tbGV2ZWwgb3BlcmF0aW9uc1xuICogLSBFeHRlcm5hbCBBUEkga2V5IGF1dGhlbnRpY2F0aW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhLFxuICAgIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/service-role.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();