/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/check-pending-payment/route";
exports.ids = ["app/api/auth/check-pending-payment/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_check_pending_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/check-pending-payment/route.ts */ \"(rsc)/./src/app/api/auth/check-pending-payment/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/check-pending-payment/route\",\n        pathname: \"/api/auth/check-pending-payment\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/check-pending-payment/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\check-pending-payment\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_check_pending_payment_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/check-pending-payment/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/auth/check-pending-payment/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function POST(req) {\n    console.log('🔥 CHECK-PENDING-PAYMENT: API called');\n    try {\n        const { email } = await req.json();\n        console.log('🔥 CHECK-PENDING-PAYMENT: Email received:', email);\n        if (!email) {\n            console.log('🔥 CHECK-PENDING-PAYMENT: No email provided');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n        console.log('🔥 CHECK-PENDING-PAYMENT: Supabase client created');\n        // Check if user exists with pending payment status\n        console.log('🔥 CHECK-PENDING-PAYMENT: Querying users...');\n        const { data: users, error: queryError } = await supabase.auth.admin.listUsers();\n        if (queryError) {\n            console.error('🔥 CHECK-PENDING-PAYMENT: Error querying users:', queryError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to check user status'\n            }, {\n                status: 500\n            });\n        }\n        console.log('🔥 CHECK-PENDING-PAYMENT: Users query successful, found', users?.users?.length || 0, 'users');\n        // Find user by email\n        const user = users.users.find((u)=>u.email === email);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                exists: false,\n                message: 'No account found with this email address.'\n            });\n        }\n        // Check user profile for status\n        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status, user_status').eq('id', user.id).single();\n        if (profile) {\n            // Check if user has pending status\n            if (profile.user_status === 'pending') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    exists: true,\n                    hasPendingPayment: true,\n                    plan: profile.subscription_tier,\n                    userId: user.id,\n                    message: `You have a pending payment for your ${profile.subscription_tier} plan. Please complete your checkout.`,\n                    signInUrl: `/auth/signin?message=complete_payment&plan=${profile.subscription_tier}&email=${encodeURIComponent(email)}`\n                });\n            }\n            // Check if user has active subscription\n            if (profile.user_status === 'active' && profile.subscription_status === 'active') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    exists: true,\n                    hasPendingPayment: false,\n                    hasActiveSubscription: true,\n                    tier: profile.subscription_tier,\n                    message: `You already have an active ${profile.subscription_tier} account. Please sign in to access your dashboard.`,\n                    signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`\n                });\n            }\n        }\n        // Fallback: Check legacy user metadata for pending payment status\n        const paymentStatus = user.user_metadata?.payment_status;\n        const userPlan = user.user_metadata?.plan;\n        if (paymentStatus === 'pending' && userPlan && [\n            'starter',\n            'professional',\n            'enterprise'\n        ].includes(userPlan)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                exists: true,\n                hasPendingPayment: true,\n                plan: userPlan,\n                userId: user.id,\n                message: `You have a pending payment for your ${userPlan} plan. Please complete your checkout.`,\n                signInUrl: `/auth/signin?message=complete_payment&plan=${userPlan}&email=${encodeURIComponent(email)}`\n            });\n        }\n        // User exists but no clear status\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            exists: true,\n            hasPendingPayment: false,\n            hasActiveSubscription: false,\n            message: 'Account found. Please sign in to continue.',\n            signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`\n        });\n    } catch (error) {\n        console.error('🔥 CHECK-PENDING-PAYMENT: Exception:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint for testing\nasync function GET() {\n    console.log('🔥 CHECK-PENDING-PAYMENT: GET endpoint called');\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Pending payment check endpoint is working',\n        usage: 'POST /api/auth/check-pending-payment with { email: \"<EMAIL>\" }',\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/check-pending-payment/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&page=%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fcheck-pending-payment%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();