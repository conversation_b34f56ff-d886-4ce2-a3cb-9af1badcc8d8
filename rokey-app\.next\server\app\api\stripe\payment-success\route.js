/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/payment-success/route";
exports.ids = ["app/api/stripe/payment-success/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_payment_success_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/payment-success/route.ts */ \"(rsc)/./src/app/api/stripe/payment-success/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/payment-success/route\",\n        pathname: \"/api/stripe/payment-success\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/payment-success/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\payment-success\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_payment_success_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/payment-success/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/stripe/payment-success/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_2__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2024-06-20'\n});\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const sessionId = searchParams.get('session_id');\n        const plan = searchParams.get('plan');\n        if (!sessionId) {\n            console.error('No session ID provided');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=no_session', req.url));\n        }\n        console.log('Processing payment success for session:', sessionId);\n        // Retrieve the checkout session from Stripe\n        const session = await stripe.checkout.sessions.retrieve(sessionId);\n        if (!session) {\n            console.error('Session not found:', sessionId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=session_not_found', req.url));\n        }\n        // Get user ID from session metadata\n        const userId = session.metadata?.user_id;\n        if (!userId || userId === 'pending_signup') {\n            console.error('No user ID in session metadata');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=no_user_id', req.url));\n        }\n        console.log('Found user ID in session:', userId);\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n        // Check if user is now active (webhook should have processed by now)\n        const { data: profile, error: profileError } = await supabase.from('user_profiles').select('user_status, subscription_status, subscription_tier').eq('id', userId).single();\n        if (profileError) {\n            console.error('Error fetching user profile:', profileError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=profile_error', req.url));\n        }\n        if (!profile) {\n            console.error('User profile not found for user:', userId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=profile_not_found', req.url));\n        }\n        console.log('User profile status:', profile);\n        // If user is not yet active, wait a moment for webhook processing\n        if (profile.user_status !== 'active') {\n            console.log('User not yet active, checking again in a moment...');\n            // Wait 2 seconds and check again\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const { data: updatedProfile } = await supabase.from('user_profiles').select('user_status, subscription_status, subscription_tier').eq('id', userId).single();\n            if (updatedProfile?.user_status !== 'active') {\n                console.log('User still not active after wait, but payment succeeded - activating manually');\n                // Manually activate the user (webhook might be delayed)\n                const { error: updateError } = await supabase.from('user_profiles').update({\n                    user_status: 'active',\n                    subscription_status: 'active',\n                    updated_at: new Date().toISOString()\n                }).eq('id', userId);\n                if (updateError) {\n                    console.error('Error manually activating user:', updateError);\n                } else {\n                    console.log('User manually activated after payment success');\n                }\n            }\n        }\n        // Create a session for the user and redirect to dashboard\n        try {\n            // Get user details for session creation\n            const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);\n            if (userError || !userData.user) {\n                console.error('Error fetching user for session creation:', userError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(`/success?session_id=${sessionId}&plan=${plan}&manual_signin=true`, req.url));\n            }\n            // Create a session token for the user\n            const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({\n                type: 'magiclink',\n                email: userData.user.email,\n                options: {\n                    redirectTo: `${req.nextUrl.origin}/dashboard?payment_success=true&plan=${plan}`\n                }\n            });\n            if (sessionError) {\n                console.error('Error generating session link:', sessionError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(`/success?session_id=${sessionId}&plan=${plan}&manual_signin=true`, req.url));\n            }\n            console.log('Generated session link, redirecting user to auto sign-in');\n            // Redirect to the magic link which will automatically sign them in and redirect to dashboard\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(sessionData.properties.action_link);\n        } catch (error) {\n            console.error('Error in automatic sign-in process:', error);\n            // Fallback: redirect to success page with manual sign-in required\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(`/success?session_id=${sessionId}&plan=${plan}&manual_signin=true`, req.url));\n        }\n    } catch (error) {\n        console.error('Error in payment success handler:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/pricing?error=payment_success_error', req.url));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/payment-success/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/stripe","vendor-chunks/qs","vendor-chunks/object-inspect","vendor-chunks/get-intrinsic","vendor-chunks/side-channel-list","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/side-channel-map","vendor-chunks/side-channel","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fpayment-success%2Froute&page=%2Fapi%2Fstripe%2Fpayment-success%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fpayment-success%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();