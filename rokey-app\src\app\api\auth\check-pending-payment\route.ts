import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';

export async function POST(req: NextRequest) {
  console.log('🔥 CHECK-PENDING-PAYMENT: API called');

  try {
    const { email } = await req.json();
    console.log('🔥 CHECK-PENDING-PAYMENT: Email received:', email);

    if (!email) {
      console.log('🔥 CHECK-PENDING-PAYMENT: No email provided');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    const supabase = createServiceRoleClient();
    console.log('🔥 CHECK-PENDING-PAYMENT: Supabase client created');

    // Check if user exists with pending payment status
    console.log('🔥 CHECK-PENDING-PAYMENT: Querying users...');
    const { data: users, error: queryError } = await supabase.auth.admin.listUsers();

    if (queryError) {
      console.error('🔥 CHECK-PENDING-PAYMENT: Error querying users:', queryError);
      return NextResponse.json(
        { error: 'Failed to check user status' },
        { status: 500 }
      );
    }

    console.log('🔥 CHECK-PENDING-PAYMENT: Users query successful, found', users?.users?.length || 0, 'users');

    // Find user by email
    const user = users.users.find(u => u.email === email);

    if (!user) {
      return NextResponse.json({
        exists: false,
        message: 'No account found with this email address.'
      });
    }

    // Check user profile for status
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('subscription_tier, subscription_status, user_status')
      .eq('id', user.id)
      .single();

    if (profile) {
      // Check if user has pending status
      if (profile.user_status === 'pending') {
        return NextResponse.json({
          exists: true,
          hasPendingPayment: true,
          plan: profile.subscription_tier,
          userId: user.id,
          message: `You have a pending payment for your ${profile.subscription_tier} plan. Please complete your checkout.`,
          signInUrl: `/auth/signin?message=complete_payment&plan=${profile.subscription_tier}&email=${encodeURIComponent(email)}`
        });
      }

      // Check if user has active subscription
      if (profile.user_status === 'active' && profile.subscription_status === 'active') {
        return NextResponse.json({
          exists: true,
          hasPendingPayment: false,
          hasActiveSubscription: true,
          tier: profile.subscription_tier,
          message: `You already have an active ${profile.subscription_tier} account. Please sign in to access your dashboard.`,
          signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`
        });
      }
    }

    // Fallback: Check legacy user metadata for pending payment status
    const paymentStatus = user.user_metadata?.payment_status;
    const userPlan = user.user_metadata?.plan;

    if (paymentStatus === 'pending' && userPlan && ['starter', 'professional', 'enterprise'].includes(userPlan)) {
      return NextResponse.json({
        exists: true,
        hasPendingPayment: true,
        plan: userPlan,
        userId: user.id,
        message: `You have a pending payment for your ${userPlan} plan. Please complete your checkout.`,
        signInUrl: `/auth/signin?message=complete_payment&plan=${userPlan}&email=${encodeURIComponent(email)}`
      });
    }

    // User exists but no clear status
    return NextResponse.json({
      exists: true,
      hasPendingPayment: false,
      hasActiveSubscription: false,
      message: 'Account found. Please sign in to continue.',
      signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`
    });

  } catch (error) {
    console.error('🔥 CHECK-PENDING-PAYMENT: Exception:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint for testing
export async function GET() {
  console.log('🔥 CHECK-PENDING-PAYMENT: GET endpoint called');
  return NextResponse.json({
    message: 'Pending payment check endpoint is working',
    usage: 'POST /api/auth/check-pending-payment with { email: "<EMAIL>" }',
    timestamp: new Date().toISOString()
  });
}
